worker_processes  1;
error_log  logs/error.log;
pid        run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    sendfile      on;
    keepalive_timeout  65;

    # === 原有的8100端口代理（保留） ===
    server {
        listen       8100;
        server_name  ************;

        # 处理重定向，确保端口号不丢失
        proxy_redirect ~^https?://([^/]+)(.*)$ http://************:8100$2;
        
        location / {
            proxy_pass http://***********;
            proxy_set_header Host ***********;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto http;
            proxy_set_header X-Forwarded-Host ************:8100;
            
            client_max_body_size 0;
            proxy_read_timeout 300s;
            proxy_send_timeout 300s;
            proxy_connect_timeout 60s;
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
            proxy_redirect off;
        }
        
        location ~* ^/(users|session|login|auth) {
            proxy_pass http://***********;
            proxy_set_header Host ***********;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto http;
            proxy_set_header X-Forwarded-Host ************:8100;
            proxy_redirect ~^https?://([^/]+)(.*)$ http://************:8100$2;
            
            client_max_body_size 0;
            proxy_read_timeout 300s;
            proxy_send_timeout 300s;
            proxy_connect_timeout 60s;
            proxy_buffering on;
        }
    }

    # === 80端口代理（用于域名访问） ===
    # server {
    #     listen       80;
    #     server_name  *********** localhost;

    #     # 处理重定向，保持原始域名（无端口）
    #     proxy_redirect ~^https?://([^/]+)(.*)$ http://***********$2;
        
    #     location / {
    #         proxy_pass http://***********;
    #         proxy_set_header Host ***********;
    #         proxy_set_header X-Real-IP $remote_addr;
    #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #         proxy_set_header X-Forwarded-Proto http;
    #         proxy_set_header X-Forwarded-Host ***********;
            
    #         client_max_body_size 0;
    #         proxy_read_timeout 300s;
    #         proxy_send_timeout 300s;
    #         proxy_connect_timeout 60s;
    #         proxy_buffering on;
    #         proxy_buffer_size 4k;
    #         proxy_buffers 8 4k;
    #         proxy_redirect off;
            
    #         # 添加CORS头
    #         add_header Access-Control-Allow-Origin *;
    #         add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
    #         add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
    #     }
        
    #     location ~* ^/(users|session|login|auth) {
    #         proxy_pass http://***********;
    #         proxy_set_header Host ***********;
    #         proxy_set_header X-Real-IP $remote_addr;
    #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #         proxy_set_header X-Forwarded-Proto http;
    #         proxy_set_header X-Forwarded-Host ***********;
    #         proxy_redirect ~^https?://([^/]+)(.*)$ http://***********$2;
            
    #         client_max_body_size 0;
    #         proxy_read_timeout 300s;
    #         proxy_send_timeout 300s;
    #         proxy_connect_timeout 60s;
    #         proxy_buffering on;
    #     }
    # }
}
