@echo off
echo ========================================
echo    GitLab DNS 域名访问配置脚本
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] 检测到管理员权限
) else (
    echo [错误] 需要管理员权限！
    echo 请右键点击此脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo.
echo 正在配置DNS域名解析...

:: 备份hosts文件
if not exist "C:\Windows\System32\drivers\etc\hosts.backup" (
    copy "C:\Windows\System32\drivers\etc\hosts" "C:\Windows\System32\drivers\etc\hosts.backup" >nul
    echo [OK] 已备份原始hosts文件
)

:: 检查是否已存在配置
findstr /C:"************    ***********" "C:\Windows\System32\drivers\etc\hosts" >nul
if %errorLevel% == 0 (
    echo [提示] DNS配置已存在，跳过添加
) else (
    :: 添加DNS解析
    echo. >> "C:\Windows\System32\drivers\etc\hosts"
    echo # GitLab DNS Proxy Configuration >> "C:\Windows\System32\drivers\etc\hosts"
    echo ************    *********** >> "C:\Windows\System32\drivers\etc\hosts"
    echo [OK] 已添加DNS解析配置
)

:: 刷新DNS缓存
echo.
echo 正在刷新DNS缓存...
ipconfig /flushdns >nul
echo [OK] DNS缓存已刷新

:: 测试连通性
echo.
echo 正在测试连通性...
ping -n 3 *********** | findstr /C:"来自 ************" >nul
if %errorLevel% == 0 (
    echo [OK] DNS解析配置成功！
) else (
    echo [警告] DNS解析可能未生效，请稍后重试
)

echo.
echo ========================================
echo           配置完成！
echo ========================================
echo.
echo 现在您可以在浏览器中访问：
echo http://***********/solution/ampcon-base
echo.
echo 如需恢复原始配置，请运行：
echo restore_windows_dns.bat
echo.
pause
